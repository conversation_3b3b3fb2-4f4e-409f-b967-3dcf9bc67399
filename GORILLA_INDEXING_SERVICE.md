# GorillaIndexingService Documentation

## Overview

The `GorillaIndexingService` is a comprehensive search service that implements intelligent search patterns for Rwanda's geographical data according to the rules defined in `CLAUDE.md`. It provides fast, cached, and pattern-aware search functionality for provinces, districts, sectors, cells, villages, and places.

## Features

### 1. **Coordinate Detection & Validation**
- Automatically detects latitude/longitude coordinates in search queries
- Validates coordinates are within Rwanda's boundaries
- Returns location information for valid coordinates

### 2. **Multi-Language Pattern Recognition**
- Supports Kinyarwanda (rw), English (en), and French (fr)
- Recognizes language-specific location prefixes:
  - **Kinyarwanda**: "Akarere ka", "Umurenge wa", "Akagari ka", "Umudugudu wa"
  - **English**: "District of", "Sector of", "Cell of", "Village of"  
  - **French**: "District de", "Secteur de", "Cellule de", "Village de"

### 3. **Hierarchy Pattern Detection**
- Automatically detects comma-separated hierarchical searches
- Maps term count to location types:
  - 2 terms → District search
  - 3 terms → Sector search
  - 4 terms → Cell search
  - 5 terms → Village search
  - 6+ terms → Place search

### 4. **Intelligent Caching**
- Caches provinces for 1 hour
- Caches coordinate-based searches
- Optimized for performance with minimal database queries

### 5. **API-Friendly Responses**
- Removes GeoJSON data for API responses
- Consistent response structure
- Language-aware formatting

## Usage Examples

### Basic Search
```php
use App\Services\GorillaIndexingService;
use App\Data\SearchData;

$service = new GorillaIndexingService();

// Search for districts
$data = new SearchData('Kigali', 'en', 'district');
$result = $service->search($data);

// API search (removes GeoJSON)
$apiResult = $service->apiSearch($data);
```

### Coordinate Search
```php
// Latitude/longitude search
$data = new SearchData('-1.9441, 30.0619', 'en', 'all');
$result = $service->search($data);
```

### Pattern-Based Search
```php
// Kinyarwanda pattern
$data = new SearchData('Akarere ka Gasabo', 'rw', 'all');
$result = $service->search($data);

// Hierarchy pattern
$data = new SearchData('Kigali, Gasabo, Kimisagara', 'en', 'all');
$result = $service->search($data);
```

### Direct Coordinate Search
```php
use App\Data\SearchLatLongData;

$coordData = new SearchLatLongData('-1.9441', '30.0619');
$result = $service->searchLatitudeLongitude($coordData);
```

## API Endpoint

The service is accessible via the API endpoint:

```
POST /api/gorilla/search
```

**Request Body:**
```json
{
    "searchQuery": "Kigali",
    "lang": "en",
    "filterData": "district"
}
```

**Response Structure:**
```json
{
    "searchQuery": "Kigali",
    "provinces": [],
    "districts": [
        {
            "id": 1,
            "name": "Kigali District",
            "code": "KGL",
            "latitude": "-1.9441",
            "longitude": "30.0619",
            "address": "Kigali City",
            "geojson": {...}
        }
    ],
    "sectors": [],
    "cells": [],
    "villages": [],
    "places": []
}
```

## Search Rules Implementation

The service implements all rules from `CLAUDE.md`:

1. **Coordinate Validation**: Checks if coordinates are valid lat/long and within Rwanda
2. **Filter Processing**: Handles specific filters vs. "all" filter logic
3. **Pattern Detection**: Implements hierarchy and language pattern detection
4. **Caching**: Provides standalone caching methods for all location types
5. **API Optimization**: Removes GeoJSON for API requests
6. **Language Support**: Includes language-based filtering and formatting

## Performance Optimizations

- **Cached Provinces**: 1-hour cache for province data
- **Efficient Queries**: Uses Laravel Scout for fast text search
- **Relationship Eager Loading**: Minimizes N+1 query problems
- **Coordinate Caching**: Caches coordinate-based location lookups
- **Limited Results**: Caps results at 10 items per category for performance

## Error Handling

- Graceful handling of invalid coordinates
- Empty result sets for failed searches
- Logging of coordinate search errors
- Fallback to general search when patterns don't match

## Testing

The service includes comprehensive tests covering:
- Basic search functionality
- Coordinate detection and validation
- Pattern recognition
- API response formatting
- Error handling scenarios

Run tests with:
```bash
php artisan test tests/Feature/GorillaIndexingServiceTest.php
```
