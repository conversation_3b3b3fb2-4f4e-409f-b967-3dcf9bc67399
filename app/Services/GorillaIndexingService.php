<?php

namespace App\Services;

use App\Data\SearchData;
use App\Data\SearchLatLongData;
use App\Enums\LocationTypeEnums;
use App\Models\Cell;
use App\Models\District;
use App\Models\PlaceMapItem;
use App\Models\Province;
use App\Models\Sector;
use App\Models\Village;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GorillaIndexingService
{
    private array $searchPatterns;

    public function __construct()
    {
        $this->initializeSearchPatterns();
    }

    public function search(SearchData $data): array
    {

        if ($this->isLatLongQuery($data->searchQuery)) {
            return $this->handleLatLongSearch($data);
        }

        $searchDetails = $this->processSearchQuery($data);

        return $this->executeSearch($searchDetails);
    }

    public function apiSearch(SearchData $data): array
    {
        $result = $this->search($data);
        return $this->formatForApi($result);
    }

    public function searchLatitudeLongitude(SearchLatLongData $data): Collection
    {
        if (!$this->isValidRwandaCoordinates($data->latitude, $data->longitude)) {
            return collect();
        }

        return $this->findLocationByCoordinates($data->latitude, $data->longitude);
    }

    private function initializeSearchPatterns(): void
    {
        $this->searchPatterns = [
            'rw' => [
                'Akarere ka' => LocationTypeEnums::DISTRICT->value,
                'Umurenge wa' => LocationTypeEnums::SECTOR->value,
                'Akagari ka' => LocationTypeEnums::CELL->value,
                'Umudugudu wa' => LocationTypeEnums::VILLAGE->value,
            ],
            'en' => [
                'District of' => LocationTypeEnums::DISTRICT->value,
                'Sector of' => LocationTypeEnums::SECTOR->value,
                'Cell of' => LocationTypeEnums::CELL->value,
                'Village of' => LocationTypeEnums::VILLAGE->value,
            ],
            'fr' => [
                'District de' => LocationTypeEnums::DISTRICT->value,
                'Secteur de' => LocationTypeEnums::SECTOR->value,
                'Cellule de' => LocationTypeEnums::CELL->value,
                'Village de' => LocationTypeEnums::VILLAGE->value,
            ],
        ];
    }

    private function isLatLongQuery(string $query): bool
    {
        $parts = explode(',', trim($query));
        if (count($parts) !== 2) {
            return false;
        }

        $lat = trim($parts[0]);
        $lng = trim($parts[1]);

        return is_numeric($lat) && is_numeric($lng) &&
               $lat >= -90 && $lat <= 90 &&
               $lng >= -180 && $lng <= 180;
    }

    private function handleLatLongSearch(SearchData $data): array
    {
        $parts = explode(',', trim($data->searchQuery));
        $lat = trim($parts[0]);
        $lng = trim($parts[1]);

        if (!$this->isValidRwandaCoordinates($lat, $lng)) {
            return $this->emptySearchResult();
        }

        $latLongData = new SearchLatLongData($lat, $lng);
        $result = $this->searchLatitudeLongitude($latLongData);

        return [
            'searchQuery' => $data->searchQuery,
            'provinces' => [],
            'districts' => [],
            'sectors' => [],
            'cells' => [],
            'villages' => $result->toArray(),
            'places' => [],
        ];
    }

    private function isValidRwandaCoordinates(string $lat, string $lng): bool
    {
        // Rwanda approximate bounds: lat: -2.9 to -1.0, lng: 28.8 to 30.9
        $latitude = (float) $lat;
        $longitude = (float) $lng;

        return $latitude >= -2.9 && $latitude <= -1.0 &&
               $longitude >= 28.8 && $longitude <= 30.9;
    }

    private function processSearchQuery(SearchData $data): array
    {
        // Handle specific filter types first
        if ($data->filterData !== 'all') {
            return $this->handleSpecificFilter($data);
        }

        // Handle 'all' filter with pattern detection
        return $this->handleAllFilter($data);
    }

    private function handleSpecificFilter(SearchData $data): array
    {
        return [
            'lang' => $data->lang,
            'searchQuery' => $data->searchQuery,
            'filterData' => [$data->filterData],
            'searchType' => 'specific'
        ];
    }

    private function handleAllFilter(SearchData $data): array
    {
        $query = trim($data->searchQuery);

        // Step 1: Check for language-specific patterns
        $patternResult = $this->detectLanguagePattern($query, $data->lang);
        if ($patternResult) {
            return $patternResult;
        }

        // Step 2: Check for comma-separated hierarchy pattern
        $hierarchyResult = $this->detectHierarchyPattern($query, $data->lang);
        if ($hierarchyResult) {
            return $hierarchyResult;
        }

        // Step 3: Default to search all fields
        return [
            'lang' => $data->lang,
            'searchQuery' => $query,
            'filterData' => [
                LocationTypeEnums::DISTRICT->value,
                LocationTypeEnums::SECTOR->value,
                LocationTypeEnums::CELL->value,
                LocationTypeEnums::VILLAGE->value,
                'place'
            ],
            'searchType' => 'general'
        ];
    }

    private function detectLanguagePattern(string $query, string $lang): ?array
    {
        $patterns = $this->searchPatterns[$lang] ?? [];

        // Sort patterns by length (longest first) for accurate matching
        uksort($patterns, fn($a, $b) => strlen($b) <=> strlen($a));

        foreach ($patterns as $pattern => $type) {
            if (stripos($query, $pattern) === 0) {
                $searchQuery = trim(substr($query, strlen($pattern)));

                return [
                    'lang' => $lang,
                    'searchQuery' => $searchQuery,
                    'filterData' => [$type],
                    'searchType' => 'pattern',
                    'originalPattern' => $pattern
                ];
            }
        }

        return null;
    }

    private function detectHierarchyPattern(string $query, string $lang): ?array
    {
        $terms = array_map('trim', explode(',', $query));
        $termCount = count($terms);

        if ($termCount < 2) {
            return null;
        }

        // Check for language patterns in terms and extract clean search terms
        $cleanTerms = [];
        $hasLanguagePatterns = false;

        foreach ($terms as $term) {
            $cleanTerm = $this->extractCleanTermFromPattern($term, $lang);
            if ($cleanTerm !== $term) {
                $hasLanguagePatterns = true;
            }
            $cleanTerms[] = $cleanTerm;
        }

        // Map term count to location hierarchy
        $hierarchy = [
            2 => [LocationTypeEnums::DISTRICT->value],
            3 => [LocationTypeEnums::SECTOR->value],
            4 => [LocationTypeEnums::CELL->value],
            5 => [LocationTypeEnums::VILLAGE->value],
            6 => ['place']
        ];

        $filterData = $hierarchy[$termCount] ?? [
            LocationTypeEnums::DISTRICT->value,
            LocationTypeEnums::SECTOR->value,
            LocationTypeEnums::CELL->value,
            LocationTypeEnums::VILLAGE->value,
            'place'
        ];

        // For hierarchy patterns, we search using individual terms, not the full query
        $searchQuery = $hasLanguagePatterns ? implode(' ', array_unique($cleanTerms)) : implode(' ', array_unique($terms));

        return [
            'lang' => $lang,
            'searchQuery' => $searchQuery,
            'filterData' => $filterData,
            'searchType' => 'hierarchy',
            'termCount' => $termCount,
            'terms' => $terms,
            'cleanTerms' => $cleanTerms,
            'hasLanguagePatterns' => $hasLanguagePatterns
        ];
    }

    private function extractCleanTermFromPattern(string $term, string $lang): string
    {
        $patterns = $this->searchPatterns[$lang] ?? [];

        // Sort patterns by length (longest first) for accurate matching
        uksort($patterns, fn($a, $b) => strlen($b) <=> strlen($a));

        foreach (array_keys($patterns) as $pattern) {
            if (stripos($term, $pattern) === 0) {
                return trim(substr($term, strlen($pattern)));
            }
        }

        return $term;
    }

    private function executeSearch(array $searchDetails): array
    {
        $result = $this->emptySearchResult();
        $result['searchQuery'] = $searchDetails['searchQuery'];

        foreach ($searchDetails['filterData'] as $filterType) {
            switch ($filterType) {
                case LocationTypeEnums::PROVINCE->value:
                    $result['provinces'] = $this->searchProvinces($searchDetails);
                    break;
                case LocationTypeEnums::DISTRICT->value:
                    $result['districts'] = $this->searchDistricts($searchDetails);
                    break;
                case LocationTypeEnums::SECTOR->value:
                    $result['sectors'] = $this->searchSectors($searchDetails);
                    break;
                case LocationTypeEnums::CELL->value:
                    $result['cells'] = $this->searchCells($searchDetails);
                    break;
                case LocationTypeEnums::VILLAGE->value:
                    $result['villages'] = $this->searchVillages($searchDetails);
                    break;
                case 'place':
                    $result['places'] = $this->searchPlaces($searchDetails);
                    break;
            }
        }

        return $result;
    }

    private function formatForApi(array $result): array
    {
        // Remove GeoJSON data for API responses
        foreach (['provinces', 'districts', 'sectors', 'cells', 'villages', 'places'] as $type) {
            if (isset($result[$type])) {
                $result[$type] = array_map(function ($item) {
                    unset($item['geojson'], $item['code']);
                    return $item;
                }, $result[$type]);
            }
        }

        return $result;
    }

    private function searchProvinces(array $searchDetails): array
    {
        return $this->getCachedProvinces()
            ->filter(function ($province) use ($searchDetails) {
                return $this->matchesSearchQuery($province, $searchDetails['searchQuery'], ['name_en', 'name_local']);
            })
            ->values()
            ->toArray();
    }

    private function searchDistricts(array $searchDetails): array
    {
        $searchQuery = $searchDetails['searchQuery'];

        // For hierarchy patterns, try individual terms if the combined search fails
        if (isset($searchDetails['searchType']) && $searchDetails['searchType'] === 'hierarchy') {
            $results = collect();

            // Try searching with each unique term
            $terms = isset($searchDetails['cleanTerms']) ? $searchDetails['cleanTerms'] : $searchDetails['terms'];
            $uniqueTerms = array_unique(array_filter($terms, fn($term) => !empty(trim($term))));

            foreach ($uniqueTerms as $term) {
                $termResults = District::search(trim($term))
                    ->query(function ($query) {
                        $query->with(['province:id,name_en,name_local'])
                            ->select('id', 'name', 'code', 'province_id', 'geojson', 'latitude', 'longitude')
                            ->limit(5);
                    })
                    ->get();

                $results = $results->merge($termResults);
            }

            // Remove duplicates and limit results
            $results = $results->unique('id')->take(10);
        } else {
            // Regular search
            $results = District::search($searchQuery)
                ->query(function ($query) {
                    $query->with(['province:id,name_en,name_local'])
                        ->select('id', 'name', 'code', 'province_id', 'geojson', 'latitude', 'longitude')
                        ->limit(10);
                })
                ->get();
        }

        return $results->map(function ($district) use ($searchDetails) {
            return $this->formatLocationResult($district, 'district', $searchDetails);
        })->toArray();
    }

    private function searchSectors(array $searchDetails): array
    {
        $searchQuery = $searchDetails['searchQuery'];

        // For hierarchy patterns, try individual terms if the combined search fails
        if (isset($searchDetails['searchType']) && $searchDetails['searchType'] === 'hierarchy') {
            $results = collect();

            // Try searching with each unique term
            $terms = isset($searchDetails['cleanTerms']) ? $searchDetails['cleanTerms'] : $searchDetails['terms'];
            $uniqueTerms = array_unique(array_filter($terms, fn($term) => !empty(trim($term))));

            foreach ($uniqueTerms as $term) {
                $termResults = Sector::search(trim($term))
                    ->query(function ($query) {
                        $query->select('id', 'name', 'code', 'district_id', 'geojson', 'latitude', 'longitude')
                            ->with(['district:id,name,province_id', 'district.province:id,name_en,name_local'])
                            ->limit(5);
                    })
                    ->get();

                $results = $results->merge($termResults);
            }

            // Remove duplicates and limit results
            $results = $results->unique('id')->take(10);
        } else {
            // Regular search
            $results = Sector::search($searchQuery)
                ->query(function ($query) {
                    $query->select('id', 'name', 'code', 'district_id', 'geojson', 'latitude', 'longitude')
                        ->with(['district:id,name,province_id', 'district.province:id,name_en,name_local'])
                        ->limit(10);
                })
                ->get();
        }

        return $results->map(function ($sector) use ($searchDetails) {
            return $this->formatLocationResult($sector, 'sector', $searchDetails);
        })->toArray();
    }

    private function searchCells(array $searchDetails): array
    {
        $searchQuery = $searchDetails['searchQuery'];

        // For hierarchy patterns, try individual terms if the combined search fails
        if (isset($searchDetails['searchType']) && $searchDetails['searchType'] === 'hierarchy') {
            $results = collect();

            // Try searching with each unique term
            $terms = isset($searchDetails['cleanTerms']) ? $searchDetails['cleanTerms'] : $searchDetails['terms'];
            $uniqueTerms = array_unique(array_filter($terms, fn($term) => !empty(trim($term))));

            foreach ($uniqueTerms as $term) {
                $termResults = Cell::search(trim($term))
                    ->query(function ($query) {
                        $query->select('id', 'name', 'code', 'sector_id', 'geojson', 'latitude', 'longitude')
                            ->with(['sector:id,name,district_id', 'sector.district:id,name,province_id', 'sector.district.province:id,name_en,name_local'])
                            ->limit(5);
                    })
                    ->get();

                $results = $results->merge($termResults);
            }

            // Remove duplicates and limit results
            $results = $results->unique('id')->take(10);
        } else {
            // Regular search
            $results = Cell::search($searchQuery)
                ->query(function ($query) {
                    $query->select('id', 'name', 'code', 'sector_id', 'geojson', 'latitude', 'longitude')
                        ->with(['sector:id,name,district_id', 'sector.district:id,name,province_id', 'sector.district.province:id,name_en,name_local'])
                        ->limit(10);
                })
                ->get();
        }

        return $results->map(function ($cell) use ($searchDetails) {
            return $this->formatLocationResult($cell, 'cell', $searchDetails);
        })->toArray();
    }

    private function searchVillages(array $searchDetails): array
    {
        $searchQuery = $searchDetails['searchQuery'];

        // For hierarchy patterns, try individual terms if the combined search fails
        if (isset($searchDetails['searchType']) && $searchDetails['searchType'] === 'hierarchy') {
            $results = collect();

            // Try searching with each unique term
            $terms = isset($searchDetails['cleanTerms']) ? $searchDetails['cleanTerms'] : $searchDetails['terms'];
            $uniqueTerms = array_unique(array_filter($terms, fn($term) => !empty(trim($term))));

            foreach ($uniqueTerms as $term) {
                $termResults = Village::search(trim($term))
                    ->query(function ($query) {
                        $query->select('id', 'name', 'cell_id', 'code', 'geojson', 'latitude', 'longitude')
                            ->with(['cell:id,name,sector_id', 'cell.sector:id,name,district_id', 'cell.sector.district:id,name,province_id', 'cell.sector.district.province:id,name_en,name_local'])
                            ->limit(5);
                    })
                    ->get();

                $results = $results->merge($termResults);
            }

            // Remove duplicates and limit results
            $results = $results->unique('id')->take(10);
        } else {
            // Regular search
            $results = Village::search($searchQuery)
                ->query(function ($query) {
                    $query->select('id', 'name', 'cell_id', 'code', 'geojson', 'latitude', 'longitude')
                        ->with(['cell:id,name,sector_id', 'cell.sector:id,name,district_id', 'cell.sector.district:id,name,province_id', 'cell.sector.district.province:id,name_en,name_local'])
                        ->limit(10);
                })
                ->get();
        }

        return $results->map(function ($village) use ($searchDetails) {
            return $this->formatLocationResult($village, 'village', $searchDetails);
        })->toArray();
    }

    private function searchPlaces(array $searchDetails): array
    {
        $searchQuery = $searchDetails['searchQuery'];

        // For hierarchy patterns, try individual terms if the combined search fails
        if (isset($searchDetails['searchType']) && $searchDetails['searchType'] === 'hierarchy') {
            $results = collect();

            // Try searching with each unique term
            $terms = isset($searchDetails['cleanTerms']) ? $searchDetails['cleanTerms'] : $searchDetails['terms'];
            $uniqueTerms = array_unique(array_filter($terms, fn($term) => !empty(trim($term))));

            foreach ($uniqueTerms as $term) {
                $termResults = PlaceMapItem::search(trim($term))
                    ->query(function ($query) {
                        $query->select('id', 'name', 'description', 'latitude', 'longitude', 'address', 'type')
                            ->where('visibility', 'public')
                            ->where('status', 'active')
                            ->limit(5);
                    })
                    ->get();

                $results = $results->merge($termResults);
            }

            // Remove duplicates and limit results
            $results = $results->unique('id')->take(10);
        } else {
            // Regular search
            $results = PlaceMapItem::search($searchQuery)
                ->query(function ($query) {
                    $query->select('id', 'name', 'description', 'latitude', 'longitude', 'address', 'type')
                        ->where('visibility', 'public')
                        ->where('status', 'active')
                        ->limit(10);
                })
                ->get();
        }

        return $results->map(function ($place) {
            return [
                'id' => $place->id,
                'name' => $place->name,
                'description' => $place->description,
                'latitude' => $place->latitude,
                'longitude' => $place->longitude,
                'address' => $place->address,
                'type' => $place->type,
            ];
        })->toArray();
    }

    private function getCachedProvinces(): Collection
    {
        return Cache::remember('gorilla_provinces', 3600, function () {
            return Province::select('id', 'name_en', 'name_local', 'code', 'geojson', 'latitude', 'longitude')
                ->get()
                ->map(function ($province) {
                    return [
                        'id' => $province->id,
                        'name_en' => $province->name_en,
                        'name_local' => $province->name_local,
                        'code' => $province->code,
                        'geojson' => $province->geojson,
                        'latitude' => $province->latitude,
                        'longitude' => $province->longitude,
                    ];
                });
        });
    }



    private function matchesSearchQuery(array $item, string $query, array $fields): bool
    {
        $query = strtolower(trim($query));

        foreach ($fields as $field) {
            if (isset($item[$field]) && str_contains(strtolower($item[$field]), $query)) {
                return true;
            }
        }

        return false;
    }

    private function formatLocationResult($location, string $type, array $searchDetails): array
    {
        $result = [
            'id' => $location->id,
            'code' => $location->code,
            'latitude' => $location->latitude,
            'longitude' => $location->longitude,
            'geojson' => $location->geojson,
        ];

        switch ($type) {
            case 'province':
                $result['name_en'] = $location->name_en;
                $result['name_local'] = $location->name_local;
                $result['name'] = $this->formatLocationName($location->name_en, $searchDetails['lang'], LocationTypeEnums::PROVINCE->value);
                break;

            case 'district':
                $result['name'] = $this->formatLocationName($location->name, $searchDetails['lang'], LocationTypeEnums::DISTRICT->value);
                $result['address'] = $location->province->name_en ?? '';
                break;

            case 'sector':
                $result['name'] = $this->formatLocationName($location->name, $searchDetails['lang'], LocationTypeEnums::SECTOR->value);
                $result['address'] = ($location->district->province->name_en ?? '') . ', ' . ($location->district->name ?? '');
                break;

            case 'cell':
                $result['name'] = $this->formatLocationName($location->name, $searchDetails['lang'], LocationTypeEnums::CELL->value);
                $result['address'] = ($location->sector->district->province->name_en ?? '') . ', ' .
                                   ($location->sector->district->name ?? '') . ', ' .
                                   ($location->sector->name ?? '');
                break;

            case 'village':
                $result['name'] = $this->formatLocationName($location->name, $searchDetails['lang'], LocationTypeEnums::VILLAGE->value);
                $result['address'] = ($location->cell->sector->district->province->name_en ?? '') . ', ' .
                                   ($location->cell->sector->district->name ?? '') . ', ' .
                                   ($location->cell->sector->name ?? '') . ', ' .
                                   ($location->cell->name ?? '');
                break;
        }

        return $result;
    }

    private function formatLocationName(string $name, string $lang, string $type): string
    {
        switch ($lang) {
            case 'rw':
                return match ($type) {
                    LocationTypeEnums::PROVINCE->value => $name,
                    LocationTypeEnums::DISTRICT->value => 'Akarere ka ' . $name,
                    LocationTypeEnums::SECTOR->value => 'Umurenge wa ' . $name,
                    LocationTypeEnums::CELL->value => 'Akagari ka ' . $name,
                    LocationTypeEnums::VILLAGE->value => 'Umudugudu wa ' . $name,
                    default => $name,
                };

            case 'en':
                return match ($type) {
                    LocationTypeEnums::PROVINCE->value => $name,
                    LocationTypeEnums::DISTRICT->value => $name . ' District',
                    LocationTypeEnums::SECTOR->value => $name . ' Sector',
                    LocationTypeEnums::CELL->value => $name . ' Cell',
                    LocationTypeEnums::VILLAGE->value => $name . ' Village',
                    default => $name,
                };

            case 'fr':
                return match ($type) {
                    LocationTypeEnums::PROVINCE->value => $name,
                    LocationTypeEnums::DISTRICT->value => 'District de ' . $name,
                    LocationTypeEnums::SECTOR->value => 'Secteur de ' . $name,
                    LocationTypeEnums::CELL->value => 'Cellule de ' . $name,
                    LocationTypeEnums::VILLAGE->value => 'Village de ' . $name,
                    default => $name,
                };

            default:
                return $name;
        }
    }

    private function findLocationByCoordinates(string $latitude, string $longitude): Collection
    {
        $pointWKT = "POINT($longitude $latitude)";
        $cacheKey = "gorilla_village_point_{$latitude}_{$longitude}";

        try {
            $village = Cache::remember($cacheKey, 3600, function () use ($pointWKT) {
                $village = DB::selectOne(
                    "SELECT
                        v.id, v.name, v.code, v.latitude, v.longitude, v.geojson,
                        c.id AS cell_id, c.name AS cell_name,
                        s.id AS sector_id, s.name AS sector_name,
                        d.id AS district_id, d.name AS district_name,
                        p.id AS province_id, p.name_en AS province_name_en, p.name_local AS province_name_local
                    FROM Village v
                    INNER JOIN Cell c ON v.cell_id = c.id
                    INNER JOIN Sector s ON c.sector_id = s.id
                    INNER JOIN District d ON s.district_id = d.id
                    INNER JOIN Province p ON d.province_id = p.id
                    WHERE ST_Contains(v.geometry, ST_GeomFromText(?, 4326))
                    LIMIT 1",
                    [$pointWKT]
                );

                // If not found, try to find nearest village within 1km
                if (!$village) {
                    $village = DB::selectOne(
                        "SELECT
                            v.id, v.name, v.code, v.latitude, v.longitude, v.geojson,
                            c.id AS cell_id, c.name AS cell_name,
                            s.id AS sector_id, s.name AS sector_name,
                            d.id AS district_id, d.name AS district_name,
                            p.id AS province_id, p.name_en AS province_name_en, p.name_local AS province_name_local
                        FROM Village v
                        INNER JOIN Cell c ON v.cell_id = c.id
                        INNER JOIN Sector s ON c.sector_id = s.id
                        INNER JOIN District d ON s.district_id = d.id
                        INNER JOIN Province p ON d.province_id = p.id
                        WHERE ST_Distance_Sphere(v.centroid, ST_GeomFromText(?, 4326)) <= 1000
                        ORDER BY ST_Distance_Sphere(v.centroid, ST_GeomFromText(?, 4326)) ASC
                        LIMIT 1",
                        [$pointWKT, $pointWKT]
                    );
                }

                return $village;
            });

            if (!$village) {
                return collect();
            }

            return collect([
                [
                    'id' => $village->id,
                    'name' => $village->name,
                    'code' => $village->code,
                    'latitude' => $village->latitude,
                    'longitude' => $village->longitude,
                    'geojson' => $village->geojson,
                    'address' => "{$village->province_name_en}, {$village->district_name}, {$village->sector_name}, {$village->cell_name}, Umudugudu wa {$village->name}"
                ]
            ]);
        } catch (\Throwable $e) {
            Log::error("Error in findLocationByCoordinates: {$e->getMessage()}", [
                'latitude' => $latitude,
                'longitude' => $longitude
            ]);
            return collect();
        }
    }

    private function emptySearchResult(): array
    {
        return [
            'searchQuery' => null,
            'provinces' => [],
            'districts' => [],
            'sectors' => [],
            'cells' => [],
            'villages' => [],
            'places' => [],
        ];
    }
}
