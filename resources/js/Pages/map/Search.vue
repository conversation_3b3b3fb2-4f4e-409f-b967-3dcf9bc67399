<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import axios from 'axios';
import { debounce } from 'lodash';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    provinces: { type: Array, default: () => [] },
});

// --- STATE REFS ---
const mapContainer = ref(null);
const map = ref(null);
const searchMode = ref(localStorage.getItem('searchMode') || 'text'); // 'text' or 'coordinates'
const searchQuery = ref('');
const coordinateForm = ref({
    latitude: '',
    longitude: '',
});
const selectedLanguage = ref(localStorage.getItem('mapLanguage') || 'en');
const selectedFilter = ref(localStorage.getItem('mapFilter') || 'all');
const panelsVisible = ref(true);
const selectedTheme = ref(localStorage.getItem('mapTheme') || 'Default');

const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);
const coordinateSearchResults = ref([]);
const lastSearchedCoords = ref(null);

const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const searchLayerIds = ref([]);
const searchSourceIds = ref([]);
const selectedResultId = ref(null);
const selectedResultType = ref(null);

// --- PRIVATE MAP VARIABLES ---
let hoverPopup = null;
let selectedPopup = null;
let hoveredFeature = { id: null, sourceId: null, type: null };
let clickMarker = null;

// --- CONFIGURATION ---
const MAP_CONFIG = {
    center: [29.8739, -1.9403],
    zoom: 8.5,
    minSearchChars: 3,
    debounceMs: 300,
    fitBoundsPadding: { top: 200, bottom: 200, left: 200, right: 200 },
    maxZoomForFit: 14,
};

const UI_CONFIG = {
    languages: [
        { code: 'rw', name: 'Kinyarwanda' },
        { code: 'en', name: 'English' },
        { code: 'fr', name: 'Français' },
    ],
    filters: [
        { code: 'all', name: 'All', icon: 'M12 4v16m8-8H4' },
        { code: 'district', name: 'District', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' },
        { code: 'sector', name: 'Sector', icon: 'M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7' },
        { code: 'cell', name: 'Cell', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2' },
        { code: 'village', name: 'Village', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
        { code: 'health_fac', name: 'Health Facility', icon: 'M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z' },
        { code: 'pattern', name: 'Pattern', icon: 'M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z' },
    ],
    layerStyles: {
        province: { fillColor: '#1A773E', borderColor: '#1C5172', fillOpacity: 0.15, borderWidth: 1.5 },
        district: { fillColor: '#1C5172', borderColor: '#1A773E', fillOpacity: 0.2, borderWidth: 1.5 },
        sector: { fillColor: '#303017', borderColor: '#1A773E', fillOpacity: 0.2, borderWidth: 1 },
        cell: { fillColor: '#1A773E', borderColor: '#1C5172', fillOpacity: 0.2, borderWidth: 0.8 },
        village: { fillColor: '#1C5172', borderColor: '#303017', fillOpacity: 0.2, borderWidth: 0.5 },
        healthFac: { fillColor: '#1A773E', borderColor: '#1C5172' },
        searchResult: { fillColor: '#1A773E', borderColor: '#1C5172', fillOpacity: 0.2, borderWidth: 0.5 },
        search_center: { fillColor: '#303017', borderColor: '#1A773E' },
    },
    highlightStyle: { color: '#1A773E', width: 3, opacity: 0.9 },
    highlightSourceId: 'highlight-source',
    highlightLayerId: 'highlight-layer',
    mapThemes: {
        'Default': {
            url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            paint: { 'raster-saturation': -0.8, 'raster-contrast': 0.2, 'raster-opacity': 0.9 }
        },
        'Satellite': {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            paint: {}
        }
    },
};

// --- COMPUTED PROPERTIES ---
const totalResults = computed(() => {
    if (searchMode.value === 'coordinates') {
        return coordinateSearchResults.value?.length || 0;
    }
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const canSearch = computed(() => {
    if (searchMode.value === 'coordinates') {
        const lat = parseFloat(coordinateForm.value.latitude);
        const lon = parseFloat(coordinateForm.value.longitude);
        return !isNaN(lat) && !isNaN(lon) && lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180;
    }
    return searchQuery.value.trim().length >= MAP_CONFIG.minSearchChars;
});

// --- LOGIC FUNCTIONS ---
const performTextSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < MAP_CONFIG.minSearchChars) {
        clearSearch(false);
        return;
    }
    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();
    try {
        const { data } = await axios.post('/api/gorilla/search', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });
        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || `Failed to fetch search results.`;
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
        updateMapLayers();
    }
}, MAP_CONFIG.debounceMs);

const performCoordinateSearch = async (latitude, longitude) => {
    if (!canSearch.value) {
        clearSearch(false);
        return;
    }
    isLoading.value = true;
    error.value = null;
    lastSearchedCoords.value = { latitude, longitude };
    const startTime = performance.now();
    try {
        const { data } = await axios.post('/map/search-latitude-langitude-json', {
            latitude,
            longitude,
            lang: selectedLanguage.value,
        });
        coordinateSearchResults.value = (data || []).map(item => ({
            ...item,
            geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
        }));
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || `Failed to fetch search results.`;
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
        updateMapLayers();
    }
};

const submitCoordinateSearch = () => {
    if (canSearch.value) {
        performCoordinateSearch(coordinateForm.value.latitude, coordinateForm.value.longitude);
    } else {
        error.value = "Please enter valid latitude (-90 to 90) and longitude (-180 to 180).";
    }
};

const clearSearch = (resetInputs = true) => {
    if (resetInputs) {
        if (searchMode.value === 'text') {
            searchQuery.value = '';
        } else {
            coordinateForm.value.latitude = '';
            coordinateForm.value.longitude = '';
            lastSearchedCoords.value = null;
        }
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    coordinateSearchResults.value = [];
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
    clearSelection();
    updateMapLayers();
};

const updateMapLayers = () => {
    if (!map.value?.isStyleLoaded()) return;

    // Clear all search-related layers and sources more thoroughly
    searchLayerIds.value.forEach(layerId => {
        if (map.value.getLayer(layerId)) map.value.removeLayer(layerId);
    });
    searchSourceIds.value.forEach(sourceId => {
        if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);
    });

    // Also clear any potential leftover sources that might not be tracked
    const allSources = map.value.getStyle().sources;
    Object.keys(allSources).forEach(sourceId => {
        if (sourceId.includes('searchResult-source-') ||
            sourceId.includes('search_center-point-source-') ||
            (sourceId.includes('-source-') && !sourceId.includes('province-source-') && !sourceId.includes('highlight-source'))) {
            if (map.value.getSource(sourceId)) {
                // Remove associated layers first
                const style = map.value.getStyle();
                style.layers.forEach(layer => {
                    if (layer.source === sourceId && map.value.getLayer(layer.id)) {
                        map.value.removeLayer(layer.id);
                    }
                });
                map.value.removeSource(sourceId);
            }
        }
    });

    searchLayerIds.value = [];
    searchSourceIds.value = [];
    clearHighlight();

    const baseLayerVisibility = totalResults.value > 0 || canSearch.value ? 'none' : 'visible';
    props.provinces.forEach(p => {
        if (p?.id && map.value.getSource(`province-source-${p.id}`)) {
            ['fill', 'border'].forEach(suffix => {
                const layerId = `province-${suffix}-${p.id}`;
                if (map.value.getLayer(layerId)) map.value.setLayoutProperty(layerId, 'visibility', baseLayerVisibility);
            });
        }
    });

    const allFeaturesForZoom = [];
    if (totalResults.value > 0) {
        if (searchMode.value === 'text') {
            ALL_RESULT_TYPES.forEach(type => {
                searchResults.value[type].forEach(result => {
                    const featureType = type.slice(0, -1);
                    if (result.geojson) {
                        const feature = { type: "Feature", geometry: result.geojson, properties: { ...result, id: result.id, type: featureType } };
                        addGeoJsonLayer(featureType, result.id, feature, true);
                        allFeaturesForZoom.push(feature);
                    } else if (result.latitude && result.longitude) {
                        const pointFeature = { type: "Feature", geometry: { type: "Point", coordinates: [result.longitude, result.latitude] }, properties: { ...result, id: result.id, type: featureType } };
                        addPointMarker(featureType, result.id, pointFeature, true);
                        allFeaturesForZoom.push(pointFeature);
                    }
                });
            });
            if (searchQuery.value.trim()) zoomToResults(allFeaturesForZoom);
        } else {
            // Coordinate search mode
            coordinateSearchResults.value.forEach(result => {
                if (result.geojson) {
                    const feature = { type: "Feature", geometry: result.geojson, properties: { ...result, id: result.id, type: 'searchResult' } };
                    addGeoJsonLayer('searchResult', result.id, feature, true);
                    allFeaturesForZoom.push(feature);
                }
            });

            if (lastSearchedCoords.value) {
                const { latitude, longitude } = lastSearchedCoords.value;
                const pointFeature = { type: "Feature", geometry: { type: "Point", coordinates: [parseFloat(longitude), parseFloat(latitude)] }, properties: { id: 'search-center', type: 'search_center' } };
                addPointMarker('search_center', 'search-center-id', pointFeature, true);
                allFeaturesForZoom.push(pointFeature);
            }

            if (allFeaturesForZoom.length > 0) zoomToResults(allFeaturesForZoom);
        }
    } else if ((searchMode.value === 'text' && !searchQuery.value.trim()) ||
               (searchMode.value === 'coordinates' && !coordinateForm.value.latitude && !coordinateForm.value.longitude)) {
        map.value.flyTo({ center: MAP_CONFIG.center, zoom: MAP_CONFIG.zoom, duration: 1000 });
    }
};

const zoomToResults = (features) => {
    if (!map.value || features.length === 0) return;
    const bounds = new maplibregl.LngLatBounds();
    let hasValidGeometry = false;
    features.forEach(feature => {
        if (feature.geometry?.type === 'Point' && feature.geometry.coordinates) {
            bounds.extend(feature.geometry.coordinates);
            hasValidGeometry = true;
        } else if (feature.geometry?.bbox) {
            bounds.extend(feature.geometry.bbox);
            hasValidGeometry = true;
        } else if (feature.geometry?.coordinates) {
            const extendCoordinates = (coords) => {
                if (Array.isArray(coords[0])) coords.forEach(extendCoordinates);
                else bounds.extend(coords);
            };
            extendCoordinates(feature.geometry.coordinates);
            hasValidGeometry = true;
        }
    });
    if (hasValidGeometry && !bounds.isEmpty()) {
        map.value.fitBounds(bounds, { padding: MAP_CONFIG.fitBoundsPadding, maxZoom: MAP_CONFIG.maxZoomForFit, duration: 1000 });
    } else if (features.length === 1 && features[0].geometry?.type === 'Point') {
        map.value.flyTo({ center: features[0].geometry.coordinates, zoom: MAP_CONFIG.maxZoomForFit, duration: 800 });
    }
};

const selectResult = (type, id) => {
    let result;
    if (searchMode.value === 'coordinates') {
        result = coordinateSearchResults.value.find(r => r.id === id);
    } else {
        result = searchResults.value[`${type}s`]?.find(r => r.id === id);
    }
    if (result) {
        displayFeaturePopup(result, type);
        if (window.innerWidth < 640) { // sm breakpoint
            panelsVisible.value = false;
        }
    }
};

const showPanels = () => {
    panelsVisible.value = true;
};

const clearSelection = () => {
    selectedResultId.value = null;
    selectedResultType.value = null;
    if (selectedPopup) {
        selectedPopup.remove();
        selectedPopup = null;
    }
    clearHighlight();
};

const clearHighlight = () => {
    if (map.value?.getSource(UI_CONFIG.highlightSourceId)) {
        map.value.getSource(UI_CONFIG.highlightSourceId).setData({ type: 'FeatureCollection', features: [] });
    }
};

// --- MAP SETUP & EVENT HANDLING ---
const resizeMap = () => map.value?.resize();

const applyMapStyle = (themeName) => {
    if (!map.value || !map.value.isStyleLoaded()) return;
    const theme = UI_CONFIG.mapThemes[themeName];
    if (theme) {
        const source = map.value.getSource('osm');
        if (source) {
            source.setTiles([theme.url]);
        }

        // Update paint properties
        const paintProperties = theme.paint || UI_CONFIG.mapThemes['Default'].paint;
        for (const key in paintProperties) {
            map.value.setPaintProperty('osm-tiles', key, paintProperties[key]);
        }

        selectedTheme.value = themeName;
        localStorage.setItem('mapTheme', themeName);
    }
};

const clearMapStyle = () => {
    applyMapStyle('Default');
    localStorage.removeItem('mapTheme');
};

const getUserLocation = () => {
    if (!navigator.geolocation) {
        error.value = "Geolocation is not supported by your browser.";
        return;
    }

    isLoading.value = true;
    navigator.geolocation.getCurrentPosition(
        (position) => {
            coordinateForm.value.latitude = position.coords.latitude.toFixed(6);
            coordinateForm.value.longitude = position.coords.longitude.toFixed(6);
            if (clickMarker) clickMarker.remove();
            clickMarker = new maplibregl.Marker({ color: '#3b82f6' })
                .setLngLat([position.coords.longitude, position.coords.latitude])
                .addTo(map.value);
            submitCoordinateSearch();
        },
        (err) => {
            isLoading.value = false;
            error.value = `Failed to get location: ${err.message}`;
            console.error(err);
        }
    );
};

onMounted(() => {
    if (!mapContainer.value) {
        error.value = 'Map container element not found.';
        return;
    }
    const initialTheme = UI_CONFIG.mapThemes[selectedTheme.value] || UI_CONFIG.mapThemes['Default'];

    try {
        map.value = new maplibregl.Map({
            container: mapContainer.value,
            style: {
                version: 8,
                sources: { osm: { type: 'raster', tiles: [initialTheme.url], tileSize: 256, attribution: initialTheme.attribution } },
                layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: initialTheme.paint }],
            },
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            attributionControl: false,
        });
        map.value.addControl(new maplibregl.NavigationControl(), 'top-right');
        map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');
        map.value.on('load', setupMap);
        window.addEventListener('resize', resizeMap);
    } catch (err) {
        console.error('Map initialization error:', err);
        error.value = 'Failed to initialize the map.';
    }
});

onUnmounted(() => {
    window.removeEventListener('resize', resizeMap);
    map.value?.remove();
});

const setupMap = () => {
    if (!map.value) return;
    if (Array.isArray(props.provinces)) {
        props.provinces.forEach(province => {
            if (province?.id && province.geojson) {
                const feature = {
                    type: "Feature",
                    geometry: typeof province.geojson === 'string' ? JSON.parse(province.geojson) : province.geojson,
                    properties: { ...province, id: province.id, type: 'province' }
                };
                if (typeof feature.properties.latitude === 'string') feature.properties.latitude = parseFloat(feature.properties.latitude);
                if (typeof feature.properties.longitude === 'string') feature.properties.longitude = parseFloat(feature.properties.longitude);
                addGeoJsonLayer('province', province.id, feature, false);
            }
        });
    }
    map.value.addSource(UI_CONFIG.highlightSourceId, { type: 'geojson', data: { type: 'FeatureCollection', features: [] } });
    map.value.addLayer({
        id: UI_CONFIG.highlightLayerId,
        type: 'line',
        source: UI_CONFIG.highlightSourceId,
        paint: { 'line-color': UI_CONFIG.highlightStyle.color, 'line-width': UI_CONFIG.highlightStyle.width, 'line-opacity': UI_CONFIG.highlightStyle.opacity },
    });

    applyMapStyle(selectedTheme.value);

    map.value.on('mousemove', handleMapHover);
    map.value.on('mouseleave', handleMapLeave);
    map.value.on('click', handleMapClick);
};

const handleMapLeave = () => {
    if (hoveredFeature.id !== null && hoveredFeature.sourceId && map.value.getSource(hoveredFeature.sourceId)) {
        map.value.setFeatureState({ source: hoveredFeature.sourceId, id: hoveredFeature.id }, { hover: false });
    }
    hoveredFeature = { id: null, sourceId: null, type: null };
    if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
    }
};

const handleMapHover = (e) => {
    if (!map.value?.isStyleLoaded()) return;
    const layersToQuery = [
        ...searchLayerIds.value.filter(id => map.value.getLayer(id) && (id.includes('-fill-') || id.includes('-point-'))),
        ...props.provinces.flatMap(p => [`province-fill-${p.id}`]).filter(id => map.value.getLayer(id))
    ].filter(id => !id.includes('-border-'));
    const features = map.value.queryRenderedFeatures(e.point, { layers: layersToQuery });
    map.value.getCanvas().style.cursor = features.length ? 'pointer' : '';
    if (hoveredFeature.id !== null && hoveredFeature.sourceId && map.value.getSource(hoveredFeature.sourceId)) {
        map.value.setFeatureState({ source: hoveredFeature.sourceId, id: hoveredFeature.id }, { hover: false });
    }
    if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
    }
    if (features.length > 0) {
        const topFeature = features[0];
        const featureId = topFeature.properties.id;
        const featureType = topFeature.properties.type;
        const sourceId = topFeature.layer.source;
        if (featureId !== undefined && sourceId && featureType) {
            map.value.setFeatureState({ source: sourceId, id: featureId }, { hover: true });
            hoveredFeature = { id: featureId, sourceId: sourceId, type: featureType };
        }
        const displayName = getDisplayName(topFeature.properties);
        if (displayName) {
            hoverPopup = new maplibregl.Popup({ closeButton: false, anchor: 'bottom-left', offset: [5, -5], className: 'hover-popup' })
                .setLngLat(e.lngLat)
                .setHTML(`<div class="hover-popup-content"><div class="hover-item"><div class="hover-indicator" style="background-color: ${UI_CONFIG.layerStyles[hoveredFeature.type]?.fillColor || '#ccc'}"></div><span>${displayName}</span></div></div>`)
                .addTo(map.value);
        }
    }
};

const handleMapClick = (e) => {
    if (!map.value) return;

    const layersToQuery = [
        ...searchLayerIds.value.filter(id => map.value.getLayer(id) && (id.includes('-fill-') || id.includes('-point-'))),
        ...props.provinces.flatMap(p => [`province-fill-${p.id}`]).filter(id => map.value.getLayer(id))
    ];
    const features = map.value.queryRenderedFeatures(e.point, { layers: layersToQuery });

    if (features.length > 0) {
        const topFeature = features[0];
        const type = topFeature.properties.type;
        const id = topFeature.properties.id;
        if (id === undefined || !type) return;

        let result;
        if (searchMode.value === 'coordinates' && type === 'searchResult') {
            result = coordinateSearchResults.value.find(r => r.id == id);
        } else if (type !== 'search_center') {
            result = searchResults.value[`${type}s`]?.find(r => r.id == id);
            if (!result && type === 'province') {
                result = props.provinces.find(p => p.id == id);
                if (result) {
                    if (typeof result.geojson === 'string') result.geojson = JSON.parse(result.geojson);
                    if (typeof result.latitude === 'string') result.latitude = parseFloat(result.latitude);
                    if (typeof result.longitude === 'string') result.longitude = parseFloat(result.longitude);
                }
            }
        }
        if (result) {
            displayFeaturePopup(result, type);
            return;
        }
    }

    // Handle coordinate search mode map clicks
    if (searchMode.value === 'coordinates') {
        const { lng, lat } = e.lngLat;
        coordinateForm.value.latitude = lat.toFixed(6);
        coordinateForm.value.longitude = lng.toFixed(6);

        if (clickMarker) {
            clickMarker.remove();
        }
        clickMarker = new maplibregl.Marker({ color: '#3b82f6' })
            .setLngLat([lng, lat])
            .addTo(map.value);

        submitCoordinateSearch();
    }
};

// --- MAP LAYER HELPERS ---
const addGeoJsonLayer = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return;
    const sourceId = `${type}-source-${id}`;
    const fillLayerId = `${type}-fill-${id}`;
    const borderLayerId = `${type}-border-${id}`;

    // Remove existing layers and source if they exist
    if (map.value.getLayer(fillLayerId)) map.value.removeLayer(fillLayerId);
    if (map.value.getLayer(borderLayerId)) map.value.removeLayer(borderLayerId);
    if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);

    const layerStyle = UI_CONFIG.layerStyles[type];
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    map.value.addLayer({
        id: fillLayerId, type: 'fill', source: sourceId,
        paint: { 'fill-color': layerStyle.fillColor, 'fill-opacity': ['case', ['boolean', ['feature-state', 'hover'], false], 0.6, layerStyle.fillOpacity] },
    });
    map.value.addLayer({
        id: borderLayerId, type: 'line', source: sourceId,
        paint: { 'line-color': layerStyle.borderColor, 'line-width': ['case', ['boolean', ['feature-state', 'hover'], false], layerStyle.borderWidth + 1, layerStyle.borderWidth] },
    });
    if (trackLayer) {
        searchLayerIds.value.push(fillLayerId, borderLayerId);
        searchSourceIds.value.push(sourceId);
    }
};

const addPointMarker = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return;
    const sourceId = `${type}-point-source-${id}`;
    const layerId = `${type}-point-${id}`;

    // Remove existing layer and source if they exist
    if (map.value.getLayer(layerId)) map.value.removeLayer(layerId);
    if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);

    const layerStyle = UI_CONFIG.layerStyles[type];
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    map.value.addLayer({
        id: layerId, type: 'circle', source: sourceId,
        paint: {
            'circle-color': layerStyle.fillColor || '#FF0000',
            'circle-radius': ['case', ['boolean', ['feature-state', 'hover'], false], 8, 6],
            'circle-stroke-color': layerStyle.borderColor || '#FFFFFF',
            'circle-stroke-width': ['case', ['boolean', ['feature-state', 'hover'], false], 2, 1.5],
            'circle-opacity': 0.9,
        },
    });
    if (trackLayer) {
        searchLayerIds.value.push(layerId);
        searchSourceIds.value.push(sourceId);
    }
};

const displayFeaturePopup = (feature, type) => {
    if (!map.value) return;
    clearSelection();
    selectedResultId.value = feature.id;
    selectedResultType.value = type;
    const highlightSource = map.value.getSource(UI_CONFIG.highlightSourceId);
    if (highlightSource) {
        if (feature.geojson) highlightSource.setData(feature.geojson);
        else highlightSource.setData({ type: 'FeatureCollection', features: [] });
    }
    const displayName = getDisplayName(feature);
    const popupContent = `
        <div class="selected-popup">
            <div class="popup-header">
                <div class="popup-indicator" style="background-color: ${UI_CONFIG.layerStyles[type]?.fillColor || '#ccc'}"></div>
                <h3>${displayName}</h3>
            </div>
            <div class="popup-content">
                <p class="popup-type">${type.charAt(0).toUpperCase() + type.slice(1)}</p>
                ${feature.address ? `<p class="popup-address">${feature.address}</p>` : ''}
                ${feature.code ? `<p class="popup-code">Code: ${feature.code}</p>` : ''}
                ${typeof feature.latitude === 'number' && typeof feature.longitude === 'number' ? `<p class="popup-coords">Lat: ${feature.latitude.toFixed(4)}, Lng: ${feature.longitude.toFixed(4)}</p>` : ''}
            </div>
        </div>`;
    const bounds = new maplibregl.LngLatBounds();
    let targetLngLat;
    if (feature.geojson?.bbox) {
        bounds.extend(feature.geojson.bbox);
        targetLngLat = bounds.getCenter();
    } else if (typeof feature.latitude === 'number' && typeof feature.longitude === 'number') {
        targetLngLat = [feature.longitude, feature.latitude];
        bounds.extend(targetLngLat);
    } else {
        targetLngLat = map.value.getCenter();
    }
    if (targetLngLat) {
        selectedPopup = new maplibregl.Popup({ closeButton: true, closeOnClick: false, anchor: 'bottom', offset: [0, -10] })
            .setLngLat(targetLngLat)
            .setHTML(popupContent)
            .addTo(map.value);
        selectedPopup.on('close', clearSelection);
        if (!bounds.isEmpty() && JSON.stringify(bounds.getNorthEast()) !== JSON.stringify(bounds.getSouthWest())) {
            map.value.fitBounds(bounds, { padding: 200, maxZoom: 14, duration: 800 });
        } else {
            map.value.flyTo({ center: targetLngLat, zoom: 14, duration: 800 });
        }
    }
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

const getPlaceholderText = () => ({
    rw: 'Shakisha ahantu...',
    en: 'Search for a location...',
    fr: 'Rechercher un lieu...',
}[selectedLanguage.value] || 'Search locations...');

// --- WATCHERS ---
watch(searchQuery, (newQuery) => {
    if (searchMode.value === 'text') {
        performTextSearch(newQuery, selectedLanguage.value, selectedFilter.value);
    }
});

watch(selectedLanguage, (newLang) => {
    localStorage.setItem('mapLanguage', newLang);
    if (searchMode.value === 'text') {
        performTextSearch(searchQuery.value, newLang, selectedFilter.value);
    } else if (canSearch.value) {
        performCoordinateSearch(coordinateForm.value.latitude, coordinateForm.value.longitude);
    }
});

watch(selectedFilter, (newFilter) => {
    localStorage.setItem('mapFilter', newFilter);
    if (searchMode.value === 'text') {
        performTextSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});

watch(searchMode, (newMode) => {
    localStorage.setItem('searchMode', newMode);
    clearSearch(true);
});

</script>

<template>
    <AppLayout title="Rwanda Geo Search">
        <div class="relative w-screen h-screen bg-cta-background-two">
            <!-- Map Container -->
            <main ref="mapContainer" class="w-full h-full"></main>

            <!-- Mobile Menu Toggle -->
            <div v-if="!panelsVisible" class="absolute top-6 left-6 z-20 sm:hidden pointer-events-auto">
                <button @click="showPanels"
                    class="bg-white text-gorilla-primary-three border border-gray-200 hover:bg-gorilla-primary hover:text-white transition-all duration-200 p-3 rounded-xl shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <!-- Premium Floating Panels -->
            <div v-if="panelsVisible" class="absolute top-0 left-0 z-10 h-full w-full sm:w-auto p-4 sm:p-6 flex flex-col gap-4 pointer-events-none">
                <!-- Premium Search & Controls Panel -->
                <div class="w-full sm:w-80 md:w-96 bg-white border border-gray-200 rounded-2xl shadow-lg pointer-events-auto">
                    <div class="p-6 space-y-6">
                        <!-- Rwanda-Inspired Header -->
                        <div class="text-center pb-4 border-b border-gray-100">
                            <h2 class="text-xl font-bold text-gorilla-primary-three">Rwanda Geo Search</h2>
                            <p class="text-sm text-gray-600 mt-1">Explore Rwanda's administrative divisions</p>
                        </div>

                        <!-- Premium Toggle Switch -->
                        <div class="flex items-center justify-center">
                            <div class="bg-cta-background-one rounded-full p-1 flex items-center">
                                <button
                                    @click="searchMode = 'text'"
                                    :class="[
                                        'px-6 py-2.5 text-sm font-medium transition-all duration-200 flex items-center gap-2 rounded-full',
                                        searchMode === 'text'
                                            ? 'bg-gorilla-primary text-white shadow-sm'
                                            : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'
                                    ]"
                                >
                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                                    </svg>
                                    Text Search
                                </button>
                                <button
                                    @click="searchMode = 'coordinates'"
                                    :class="[
                                        'px-6 py-2.5 text-sm font-medium transition-all duration-200 flex items-center gap-2 rounded-full',
                                        searchMode === 'coordinates'
                                            ? 'bg-gorilla-primary text-white shadow-sm'
                                            : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'
                                    ]"
                                >
                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Coordinates
                                </button>
                            </div>
                        </div>

                        <!-- Clean Text Search Interface -->
                        <div v-if="searchMode === 'text'" class="space-y-3">
                            <div class="relative">
                                <svg class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                                </svg>
                                <Input
                                    v-model="searchQuery"
                                    :placeholder="getPlaceholderText()"
                                    class="w-full pl-12 pr-12 py-4 text-base border border-gray-300 rounded-xl focus:ring-0 focus:border-gray-400 bg-white font-medium transition-all duration-200"
                                />
                                <div v-if="isLoading" class="absolute right-4 top-1/2 -translate-y-1/2">
                                    <svg class="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Clean Coordinate Search Interface -->
                        <div v-if="searchMode === 'coordinates'" class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <label for="latitude" class="text-sm font-semibold text-gorilla-primary-three">Latitude</label>
                                    <Input
                                        id="latitude"
                                        v-model="coordinateForm.latitude"
                                        type="number"
                                        placeholder="-1.9403"
                                        class="w-full py-3 px-4 border border-gray-300 rounded-xl focus:ring-0 focus:border-gray-400 font-medium transition-all duration-200"
                                        @keyup.enter="submitCoordinateSearch"
                                    />
                                </div>
                                <div class="space-y-2">
                                    <label for="longitude" class="text-sm font-semibold text-gorilla-primary-three">Longitude</label>
                                    <Input
                                        id="longitude"
                                        v-model="coordinateForm.longitude"
                                        type="number"
                                        placeholder="29.8739"
                                        class="w-full py-3 px-4 border border-gray-300 rounded-xl focus:ring-0 focus:border-gray-400 font-medium transition-all duration-200"
                                        @keyup.enter="submitCoordinateSearch"
                                    />
                                </div>
                            </div>
                            <div class="flex gap-3">
                                <Button
                                    @click="submitCoordinateSearch"
                                    class="flex-1 bg-gorilla-primary text-white hover:bg-gorilla-primary/90 transition-all duration-200 py-3 px-6 rounded-xl font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                                    :disabled="isLoading || !canSearch"
                                >
                                    <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Search Location
                                </Button>
                                <Button
                                    @click="getUserLocation"
                                    class="bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gorilla-primary-three transition-all duration-200 py-3 px-4 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed"
                                    :disabled="isLoading"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </Button>
                            </div>
                        </div>

                        <!-- Clean Settings Dialog -->
                        <Dialog>
                            <DialogTrigger as-child>
                                <Button class="w-full bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gorilla-primary-three transition-all duration-200 py-3 px-6 rounded-xl font-semibold">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Filters & Settings
                                </Button>
                            </DialogTrigger>
                            <DialogContent class="sm:max-w-[500px] border border-gray-200 rounded-2xl">
                                <DialogHeader>
                                    <DialogTitle class="text-2xl font-bold text-gorilla-primary-three">Search Settings</DialogTitle>
                                    <DialogDescription class="text-gray-600 font-medium">
                                        Customize your search experience with language{{ searchMode === 'text' ? ', filters,' : '' }} and map preferences.
                                    </DialogDescription>
                                </DialogHeader>
                                <div class="grid gap-6 py-6">
                                    <div class="space-y-4">
                                        <label class="text-sm font-semibold text-gorilla-primary-three">Language / Ururimi</label>
                                        <div class="bg-cta-background-one rounded-xl p-1 flex items-center">
                                            <button
                                                v-for="lang in UI_CONFIG.languages"
                                                :key="lang.code"
                                                @click="selectedLanguage = lang.code"
                                                :class="[
                                                    'flex-1 text-center px-4 py-2.5 text-sm font-medium transition-all duration-200 rounded-lg',
                                                    selectedLanguage === lang.code
                                                        ? 'bg-gorilla-primary text-white shadow-sm'
                                                        : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'
                                                ]"
                                            >
                                                {{ lang.name }}
                                            </button>
                                        </div>
                                    </div>
                                    <div v-if="searchMode === 'text'" class="space-y-4">
                                        <label class="text-sm font-semibold text-gorilla-primary-three">Search Categories</label>
                                        <div class="grid grid-cols-3 sm:grid-cols-4 gap-3">
                                            <button
                                                v-for="filter in UI_CONFIG.filters"
                                                :key="filter.code"
                                                @click="selectedFilter = filter.code"
                                                :title="filter.name"
                                                :class="[
                                                    'p-3 text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-2 aspect-square border border-gray-200 rounded-xl',
                                                    selectedFilter === filter.code
                                                        ? 'bg-gorilla-primary-two text-white border-gorilla-primary-two'
                                                        : 'bg-white text-gorilla-primary-three hover:bg-cta-background-one hover:border-gray-300'
                                                ]"
                                            >
                                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="filter.icon" />
                                                </svg>
                                                <span class="text-[10px] leading-tight text-center">{{ filter.name }}</span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <label class="text-sm font-semibold text-gorilla-primary-three">Map Style</label>
                                        <div class="grid grid-cols-2 gap-3">
                                            <button
                                                v-for="(_, name) in UI_CONFIG.mapThemes"
                                                :key="name"
                                                @click="applyMapStyle(name)"
                                                :class="[
                                                    'p-4 text-sm font-medium transition-all duration-200 flex flex-col items-center justify-center gap-3 border border-gray-200 rounded-xl',
                                                    selectedTheme === name
                                                        ? 'border-gorilla-primary-two bg-gorilla-primary-two text-white'
                                                        : 'bg-white text-gorilla-primary-three hover:bg-cta-background-one hover:border-gray-300'
                                                ]"
                                            >
                                                <div class="w-12 h-8 border-2 rounded-lg overflow-hidden"
                                                    :class="selectedTheme === name ? 'border-white/30' : 'border-gray-300'"
                                                    :style="{ backgroundColor: name === 'Default' ? '#edefeb' : '#1A773E' }">
                                                </div>
                                                <span class="font-semibold">{{ name }}</span>
                                            </button>
                                        </div>
                                        <Button
                                            @click="clearMapStyle"
                                            class="mt-3 w-full bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gorilla-primary-three transition-all duration-200 py-2.5 px-4 rounded-xl font-semibold"
                                        >
                                            Reset to Default
                                        </Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>

                <!-- Clean Search Results Panel -->
                <div v-if="((searchMode === 'text' && searchQuery) || (searchMode === 'coordinates' && (canSearch || totalResults > 0 || error))) || totalResults > 0" class="w-full sm:w-80 md:w-96 bg-white border border-gray-200 rounded-2xl pointer-events-auto flex flex-col flex-1 min-h-0">
                    <div class="px-6 py-4 border-b border-gray-100 bg-gorilla-primary text-white rounded-t-2xl">
                        <div class="text-sm font-semibold">
                            <span v-if="searchMode === 'text' && searchQuery.trim().length > 0 && !canSearch">Enter at least {{ MAP_CONFIG.minSearchChars }} characters to search.</span>
                            <span v-else-if="isLoading">🔍 Searching Rwanda locations...</span>
                            <span v-else-if="totalResults > 0">✅ {{ totalResults }} location{{ totalResults !== 1 ? 's' : '' }} found<span v-if="searchTime > 0"> in {{ searchTime }}ms</span></span>
                            <span v-else-if="searchMode === 'coordinates' && lastSearchedCoords">📍 No locations found at these coordinates.</span>
                        </div>
                    </div>

                    <div class="flex-1 overflow-y-auto">
                        <div v-if="error" class="p-6 m-4 text-center text-white bg-red-600 rounded-xl border border-red-700">
                            <p class="font-bold text-lg">⚠️ Search Error</p>
                            <p class="text-sm mt-2 font-medium opacity-90">{{ error }}</p>
                        </div>
                        <div v-else-if="!isLoading && totalResults === 0 && ((searchMode === 'text' && canSearch) || (searchMode === 'coordinates' && lastSearchedCoords))" class="p-8 text-center">
                            <div class="w-16 h-16 bg-cta-background-one rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                            <h3 class="font-bold text-lg text-gorilla-primary-three">No locations found</h3>
                            <p class="mt-2 text-sm font-medium text-gray-600">{{ searchMode === 'text' ? 'Try a different search term or check spelling.' : 'Try different coordinates within Rwanda.' }}</p>
                        </div>
                        <!-- Clean Text Search Results -->
                        <ul v-else-if="totalResults > 0 && searchMode === 'text'" class="divide-y divide-gray-100">
                            <template v-for="type in ALL_RESULT_TYPES" :key="type">
                                <li v-for="result in searchResults[type]" :key="`${type}-${result.id}`"
                                    @click="selectResult(type.slice(0, -1), result.id)"
                                    :class="[
                                        'p-4 hover:bg-cta-background-one cursor-pointer transition-all duration-200 group',
                                        selectedResultId === result.id && selectedResultType === type.slice(0, -1)
                                            ? 'bg-gorilla-primary-two text-white'
                                            : 'hover:border-l-4 hover:border-l-gray-300'
                                    ]">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex-shrink-0 w-3 h-3 rounded-full border-2"
                                            :class="selectedResultId === result.id && selectedResultType === type.slice(0, -1) ? 'border-white' : 'border-gray-300'"
                                            :style="{
                                                backgroundColor: selectedResultId === result.id && selectedResultType === type.slice(0, -1)
                                                    ? 'white'
                                                    : UI_CONFIG.layerStyles[type.slice(0, -1)]?.fillColor || '#1A773E'
                                            }">
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-semibold truncate text-gorilla-primary-three">{{ getDisplayName(result) }}</p>
                                            <p class="text-xs font-medium capitalize truncate opacity-75">
                                                {{ result.address || (result.latitude && result.longitude ? 'Geographic Coordinates' : result.code || type.slice(0, -1)) }}
                                            </p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <svg class="w-4 h-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </li>
                            </template>
                        </ul>
                        <!-- Clean Coordinate Search Results -->
                        <ul v-else-if="totalResults > 0 && searchMode === 'coordinates'" class="divide-y divide-gray-100">
                            <li v-for="result in coordinateSearchResults" :key="`result-${result.id}`"
                                @click="selectResult('searchResult', result.id)"
                                :class="[
                                    'p-4 hover:bg-cta-background-one cursor-pointer transition-all duration-200 group',
                                    selectedResultId === result.id && selectedResultType === 'searchResult'
                                        ? 'bg-gorilla-primary-two text-white'
                                        : 'hover:border-l-4 hover:border-l-gray-300'
                                ]">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0 w-3 h-3 rounded-full border-2"
                                        :class="selectedResultId === result.id && selectedResultType === 'searchResult' ? 'border-white' : 'border-gray-300'"
                                        :style="{
                                            backgroundColor: selectedResultId === result.id && selectedResultType === 'searchResult'
                                                ? 'white'
                                                : UI_CONFIG.layerStyles.searchResult.fillColor
                                        }">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold truncate text-gorilla-primary-three">{{ getDisplayName(result) }}</p>
                                        <p class="text-xs font-medium capitalize truncate opacity-75">{{ result.address || 'Administrative Division' }}</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <svg class="w-4 h-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
/* Ensure html, body, and app root are full height */
html, body, #app {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent body scroll */
}

/* Clean Map Controls */
.maplibregl-ctrl-group {
    background-color: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
}

.maplibregl-ctrl-group button {
    border-radius: 12px !important;
    color: #303017 !important;
}

.maplibregl-ctrl-group button:hover {
    background-color: #edefeb !important;
    color: #1A773E !important;
}

/* Clean Popup Styles */
.maplibregl-popup-content {
    padding: 0;
    background: transparent;
}

/* Clean Hover Popup */
.maplibregl-popup.hover-popup .maplibregl-popup-content {
    background-color: #1A773E;
    color: white;
    padding: 12px 16px;
    border: 1px solid #1C5172;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    max-width: 280px;
}
.maplibregl-popup-anchor-bottom .maplibregl-popup-tip { border-top-color: #1A773E; }
.maplibregl-popup-anchor-top .maplibregl-popup-tip { border-bottom-color: #1A773E; }
.maplibregl-popup-anchor-left .maplibregl-popup-tip { border-right-color: #1A773E; }
.maplibregl-popup-anchor-right .maplibregl-popup-tip { border-left-color: #1A773E; }

/* Clean Selected Popup */
.selected-popup {
    background-color: white;
    color: #303017;
    padding: 20px 24px;
    border-radius: 16px;
    border: 2px solid #edefeb;
    font-size: 14px;
    max-width: 320px;
}
.selected-popup .popup-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #edefeb;
}
.selected-popup .popup-indicator {
    width: 12px;
    height: 12px;
    border: 2px solid #1A773E;
    border-radius: 50%;
    flex-shrink: 0;
}
.selected-popup h3 {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    color: #303017;
}
.selected-popup .popup-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.selected-popup .popup-type, .selected-popup .popup-address, .selected-popup .popup-code, .selected-popup .popup-coords {
    font-size: 13px;
    font-weight: 500;
    color: #303017;
}

/* Clean Hover Content */
.hover-popup-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.hover-item {
    display: flex;
    align-items: center;
    gap: 8px;
}
.hover-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Custom scrollbar for results */
.overflow-y-auto::-webkit-scrollbar {
    width: 4px;
}
.overflow-y-auto::-webkit-scrollbar-track {
    background: #edefeb;
    border-radius: 2px;
}
.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #1C5172;
    border-radius: 2px;
}
.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #303017;
}
</style>