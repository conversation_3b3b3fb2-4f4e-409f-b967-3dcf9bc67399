<?php

use App\Data\SearchData;
use App\Data\SearchLatLongData;
use App\Services\GorillaIndexingService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->service = new GorillaIndexingService();
});

it('can search for districts', function () {
    $searchData = new SearchData('Kigali', 'en', 'district');
    $result = $this->service->search($searchData);
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKeys(['searchQuery', 'provinces', 'districts', 'sectors', 'cells', 'villages', 'places'])
        ->and($result['searchQuery'])->toBe('Kigali');
});

it('can detect latitude longitude coordinates', function () {
    $searchData = new SearchData('-1.9441, 30.0619', 'en', 'all');
    $result = $this->service->search($searchData);
    
    expect($result)->toBeArray()
        ->and($result['searchQuery'])->toBe('-1.9441, 30.0619');
});

it('can handle pattern search in Kinyarwanda', function () {
    $searchData = new SearchData('Akarere ka Gasabo', 'rw', 'all');
    $result = $this->service->search($searchData);
    
    expect($result)->toBeArray()
        ->and($result['searchQuery'])->toBe('Akarere ka Gasabo');
});

it('can handle hierarchy pattern search', function () {
    $searchData = new SearchData('Kigali, Gasabo', 'en', 'all');
    $result = $this->service->search($searchData);
    
    expect($result)->toBeArray()
        ->and($result['searchQuery'])->toBe('Kigali, Gasabo');
});

it('can format results for API', function () {
    $searchData = new SearchData('Kigali', 'en', 'district');
    $result = $this->service->apiSearch($searchData);
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKeys(['searchQuery', 'provinces', 'districts', 'sectors', 'cells', 'villages', 'places']);
});

it('validates Rwanda coordinates correctly', function () {
    // Valid Rwanda coordinates
    $validData = new SearchLatLongData('-1.9441', '30.0619');
    $result = $this->service->searchLatitudeLongitude($validData);
    expect($result)->toBeInstanceOf(\Illuminate\Support\Collection::class);
    
    // Invalid coordinates (outside Rwanda)
    $invalidData = new SearchLatLongData('40.7128', '-74.0060'); // New York
    $result = $this->service->searchLatitudeLongitude($invalidData);
    expect($result)->toBeInstanceOf(\Illuminate\Support\Collection::class)
        ->and($result)->toBeEmpty();
});

it('returns empty result for invalid search', function () {
    $searchData = new SearchData('', 'en', 'district');
    $result = $this->service->search($searchData);
    
    expect($result)->toBeArray()
        ->and($result)->toHaveKeys(['searchQuery', 'provinces', 'districts', 'sectors', 'cells', 'villages', 'places']);
});
