<?php

use App\Http\Controllers\GorillaIndexingController;
use App\Http\Controllers\MapController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
     return response()->json('Welcome to the Onrwanda Geo API!');
});

Route::prefix('search')->group(function () {
    Route::get('', [MapController::class, 'ApiSearch'])->name('api.search');
    Route::get('/location', [MapController::class, 'searchLocation'])->name('api.search.location');
});

Route::prefix('gorilla')->group(function () {
    Route::post('search', [GorillaIndexingController::class, 'search'])->name('api.gorilla.search');
});